/**
 * XPrinter 数据格式解析器
 * 用于解析和验证 XPrinter JSON 格式的标签模板数据
 */

// 元素类型常量
export const ELEMENT_TYPES = {
  MULTIPLE_SELECTION_LAYOUT: -1,
  TEXT: 1,
  BAR_CODE: 2,
  CANVAS: 3,
  LINE: 4,
  <PERSON>OG<PERSON>: 5,
  PICTURE: 6,
  QR_CODE: 7,
  CIRCULAR: 8,
  TIME: 9,
  TABLE: 10,
  RECTANGLE: 11
};

// 元素类型名称映射
export const ELEMENT_TYPE_NAMES = {
  [ELEMENT_TYPES.TEXT]: '文本',
  [ELEMENT_TYPES.BAR_CODE]: '条形码',
  [ELEMENT_TYPES.CANVAS]: '画布',
  [ELEMENT_TYPES.LINE]: '线条',
  [ELEMENT_TYPES.LOGO]: 'Logo',
  [ELEMENT_TYPES.PICTURE]: '图片',
  [ELEMENT_TYPES.QR_CODE]: '二维码',
  [ELEMENT_TYPES.CIRCULAR]: '圆形',
  [ELEMENT_TYPES.TIME]: '时间',
  [ELEMENT_TYPES.TABLE]: '表格',
  [ELEMENT_TYPES.RECTANGLE]: '矩形'
};

/**
 * 解析 XPrinter JSON 数据
 * @param {Object} jsonData - XPrinter JSON 数据
 * @returns {Object} 解析后的数据结构
 */
export function parseXPrinterData(jsonData) {
  try {
    if (!jsonData || typeof jsonData !== 'object') {
      throw new Error('无效的 JSON 数据');
    }

    const { width, height, data } = jsonData;

    if (!width || !height || !data) {
      throw new Error('缺少必要字段: width, height, data');
    }

    // 解析画布信息
    const canvasInfo = extractCanvasInfo(data);
    
    // 解析元素列表
    const elements = extractElements(data);

    return {
      canvas: {
        width: parseFloat(width),
        height: parseFloat(height),
        ...canvasInfo
      },
      elements,
      originalData: jsonData
    };
  } catch (error) {
    console.error('解析 XPrinter 数据失败:', error);
    throw error;
  }
}

/**
 * 提取画布信息
 * @param {Array} data - 数据数组
 * @returns {Object} 画布信息
 */
function extractCanvasInfo(data) {
  const canvasElement = data.find(item => 
    item.elementType === "3" || item.elementType === 3
  );

  if (!canvasElement) {
    return {};
  }

  return {
    os: canvasElement.os,
    versionCode: canvasElement.versionCode,
    templateBg: canvasElement.templateBg,
    cableLabelDirection: canvasElement.cableLabelDirection,
    cableLabelLength: canvasElement.cableLabelLength
  };
}

/**
 * 提取元素列表
 * @param {Array} data - 数据数组
 * @returns {Array} 元素列表
 */
function extractElements(data) {
  return data
    .filter(item => item.elementType !== "3" && item.elementType !== 3)
    .map(element => normalizeElement(element));
}

/**
 * 标准化元素数据
 * @param {Object} element - 原始元素数据
 * @returns {Object} 标准化后的元素数据
 */
function normalizeElement(element) {
  const elementType = parseInt(element.elementType);
  
  const normalized = {
    elementType,
    typeName: ELEMENT_TYPE_NAMES[elementType] || '未知',
    
    // 位置和尺寸
    x: parseFloat(element.x || 0),
    y: parseFloat(element.y || 0),
    width: parseFloat(element.width || 0),
    height: parseFloat(element.height || 0),
    
    // 通用属性
    rotational: parseFloat(element.rotational || 0),
    localization: element.localization === "true",
    controlType: element.controlType,
    takePrint: element.takePrint !== "false",
    mirrorImage: element.mirrorImage === "true",
    
    // 原始数据
    ...element
  };

  // 根据元素类型添加特定属性
  switch (elementType) {
    case ELEMENT_TYPES.TEXT:
      return normalizeTextElement(normalized);
    case ELEMENT_TYPES.QR_CODE:
      return normalizeQRCodeElement(normalized);
    case ELEMENT_TYPES.BAR_CODE:
      return normalizeBarCodeElement(normalized);
    case ELEMENT_TYPES.LINE:
      return normalizeLineElement(normalized);
    case ELEMENT_TYPES.TIME:
      return normalizeTimeElement(normalized);
    default:
      return normalized;
  }
}

/**
 * 标准化文本元素
 */
function normalizeTextElement(element) {
  return {
    ...element,
    content: element.content || '',
    textSize: parseFloat(element.textSize || 12),
    fontType: element.fontType || '0',
    hAlignment: parseInt(element.hAlignment || 1),
    bold: element.bold === "true",
    italic: element.italic === "true",
    underline: element.underline === "true",
    strikethrough: element.strikethrough === "true",
    wordSpace: parseFloat(element.wordSpace || 0),
    linesSpace: parseFloat(element.linesSpace || 0),
    lineWrap: element.lineWrap !== "false",
    blackWhiteReflection: element.blackWhiteReflection === "true"
  };
}

/**
 * 标准化二维码元素
 */
function normalizeQRCodeElement(element) {
  return {
    ...element,
    content: element.content || '',
    codeType: element.codeType,
    whiteMargin: parseFloat(element.whiteMargin || 0),
    errorCorrectionLevel: element.errorCorrectionLevel
  };
}

/**
 * 标准化条形码元素
 */
function normalizeBarCodeElement(element) {
  return {
    ...element,
    content: element.content || '123456',
    barcodeType: element.barcodeType,
    showText: element.showText !== "false",
    textAlignment: element.textAlignment,
    textSize: parseFloat(element.textSize || 12)
  };
}

/**
 * 标准化线条元素
 */
function normalizeLineElement(element) {
  return {
    ...element,
    lineType: element.lineType,
    dashType: element.dashType,
    intervalValue: parseFloat(element.intervalValue || 0),
    rotationAngle: parseFloat(element.rotationAngle || 0)
  };
}

/**
 * 标准化时间元素
 */
function normalizeTimeElement(element) {
  return {
    ...element,
    timeFormat: element.timeFormat,
    dateFormat: element.dateFormat,
    autoUpdate: element.autoUpdate !== "false",
    lockTimeStamp: element.lockTimeStamp === "true"
  };
}

/**
 * 验证元素数据
 * @param {Object} element - 元素数据
 * @returns {Array} 验证错误列表
 */
export function validateElement(element) {
  const errors = [];

  if (!element.elementType) {
    errors.push('缺少元素类型');
  }

  if (element.x === undefined || element.y === undefined) {
    errors.push('缺少位置信息');
  }

  if (element.width === undefined || element.height === undefined) {
    errors.push('缺少尺寸信息');
  }

  return errors;
}

/**
 * 获取元素统计信息
 * @param {Array} elements - 元素列表
 * @returns {Object} 统计信息
 */
export function getElementStats(elements) {
  const stats = {};
  
  elements.forEach(element => {
    const typeName = element.typeName || '未知';
    stats[typeName] = (stats[typeName] || 0) + 1;
  });

  return stats;
}
