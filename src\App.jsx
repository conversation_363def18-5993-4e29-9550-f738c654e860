import { useState, useEffect } from 'react';
import styled from 'styled-components';
import Canvas from './components/Canvas';
import DragPanel from './components/DragPanel';
import { parseXPrinterData } from './utils/xprinterParser';
import { autoLayout } from './utils/autoLayout';
import './App.css';

// 示例数据
const foodLabelData = {
  "width": 50,
  "height": 80,
  "data": [
    {
      "os": "android",
      "templateBg": "",
      "cableLabelDirection": 2,
      "cableLabelLength": 0,
      "elementType": "3",
      "versionCode": 0
    },
    {
      "lineWrap": "true",
      "underline": "false",
      "transmutationType": 1,
      "lockLocation": "false",
      "prefix": "",
      "showKeyName": false,
      "transmutationValue": "1",
      "suffix": "",
      "italic": "false",
      "mirrorImage": "false",
      "content": "品名:",
      "automaticHeightCalculation": "true",
      "textArrangementType": 0,
      "transmutationCount": "0",
      "strikethrough": "false",
      "flipX": "false",
      "transmutationNegativeNumbers": "false",
      "height": "4.571759",
      "inputDataType": "1",
      "linesSpace": "0.0",
      "fontType": "-2",
      "takePrint": "true",
      "textSize": "10.0",
      "bold": "false",
      "excelPos": -1,
      "arcAngle": 180,
      "bindKey": "",
      "controlType": "3",
      "rotationAngle": "0",
      "x": "5.5049753",
      "width": "10.821759",
      "y": "7.0534034",
      "hAlignment": "1",
      "elementType": 1,
      "blackWhiteReflection": "false",
      "wordSpace": "0.0"
    },
    {
      "takePrint": "true",
      "lockLocation": "false",
      "mirrorImage": "false",
      "intervalValue": "0.46296296",
      "controlType": "3",
      "rotationAngle": "0",
      "lineType": "1",
      "x": "13.025467",
      "width": "27.314814",
      "y": "10.499629",
      "dashType": "8",
      "elementType": 4,
      "height": "2.9755104"
    }
  ]
};

const jewelryLabelData = {
  "width": 70,
  "height": 30,
  "data": [
    {
      "os": "ios",
      "templateBg": "",
      "cableLabelDirection": 2,
      "cableLabelLength": 0,
      "elementType": "3",
      "versionCode": 0
    },
    {
      "lineWrap": "true",
      "underline": "false",
      "transmutationType": 0,
      "lockLocation": "false",
      "prefix": "",
      "showKeyName": false,
      "transmutationValue": "0",
      "suffix": "",
      "italic": "false",
      "mirrorImage": "false",
      "content": "2507130003\n爱茵珠宝  \n足金包足银\n福运套链",
      "automaticHeightCalculation": "true",
      "textArrangementType": 0,
      "transmutationCount": "0",
      "excelKey": "",
      "strikethrough": "false",
      "flipX": "false",
      "transmutationNegativeNumbers": "false",
      "height": "3.5648148",
      "inputDataType": "1",
      "linesSpace": "0.0",
      "fontType": "0",
      "takePrint": "true",
      "textSize": "8.0",
      "bold": "true",
      "excelPos": -1,
      "arcAngle": 0,
      "bindKey": "",
      "controlType": "3",
      "rotationAngle": "0",
      "x": "2.5925925",
      "width": "19.768518",
      "y": "0.8912037",
      "hAlignment": "1",
      "elementType": 1,
      "blackWhiteReflection": "false",
      "wordSpace": "0.0"
    }
  ]
};

const breadLabelData = {
  "width": 40,
  "height": 30,
  "data": [
    {
      "elementType": "3",
      "os": "ios",
      "cableLabelDirection": 2,
      "cableLabelLength": 0,
      "versionCode": "0"
    },
    {
      "transmutationType": "1",
      "elementType": 1,
      "arcAngle": 0,
      "lockLocation": "false",
      "takePrint": "true",
      "flipX": "false",
      "transmutationCount": "0",
      "controlType": "3",
      "wordSpace": "0.000",
      "inputDataType": "1",
      "automaticHeightCalculation": "true",
      "transmutationNegativeNumbers": "false",
      "bold": "false",
      "transmutationValue": "0",
      "font_scale": "2",
      "italic": "false",
      "rotationAngle": "0",
      "horizontalAlignment": "true",
      "height": "3.043",
      "strikethrough": "false",
      "mirrorImage": "false",
      "underline": "false",
      "content": "山药紫薯软欧包",
      "textArrangementType": 0,
      "textSize": "6.999000",
      "linesSpace": "0.0",
      "lineWrap": "true",
      "hAlignment": "1",
      "x": "8.387",
      "fontType": "-2",
      "blackWhiteReflection": "false",
      "y": "0.396",
      "width": "25.405"
    }
  ]
};

const AppContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-width: 750px;
  margin: 0 auto;
  background-color: #f5f5f5;

  @media (min-width: 768px) {
    flex-direction: row;
  }
`;

const Sidebar = styled.div`
  width: 100%;
  background-color: white;
  border-bottom: 1px solid #ddd;
  padding: 15px;
  overflow-y: auto;
  max-height: 200px;

  @media (min-width: 768px) {
    width: 280px;
    border-right: 1px solid #ddd;
    border-bottom: none;
    max-height: none;
    padding: 20px;
  }
`;

const MainArea = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 15px;
  overflow-y: auto;

  @media (min-width: 768px) {
    justify-content: center;
    padding: 20px;
  }
`;

const ToolBar = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 15px;
  width: 100%;
  justify-content: center;

  @media (min-width: 768px) {
    gap: 10px;
    margin-bottom: 20px;
    justify-content: flex-start;
  }
`;

const Button = styled.button`
  padding: 12px 16px;
  border: 1px solid #ddd;
  background-color: white;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  flex: 1;
  min-width: 80px;
  max-width: 150px;

  &:hover {
    background-color: #f0f0f0;
  }

  &.primary {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
    font-weight: bold;

    &:hover {
      background-color: #0056b3;
    }
  }

  @media (min-width: 768px) {
    flex: 0 0 auto;
    padding: 10px 20px;
  }
`;

const TemplateSelector = styled.select`
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-bottom: 15px;
  width: 100%;
  font-size: 14px;
  background-color: white;

  @media (min-width: 768px) {
    padding: 8px;
    margin-bottom: 20px;
    width: auto;
  }
`;

const ElementInfo = styled.div`
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
  margin-top: 15px;
  font-size: 14px;

  h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
  }

  p {
    margin: 4px 0;
    font-size: 13px;
  }

  @media (min-width: 768px) {
    padding: 15px;
    margin-top: 20px;
    font-size: inherit;

    h4 {
      margin: 0 0 10px 0;
    }

    p {
      margin: 6px 0;
      font-size: inherit;
    }
  }
`;

function App() {
  const [currentTemplate, setCurrentTemplate] = useState(null);
  const [selectedElement, setSelectedElement] = useState(null);
  const [scale, setScale] = useState(1.2); // 手机屏幕默认放大一些

  // 模板数据
  const templates = {
    food: { name: '食品标签', data: foodLabelData },
    jewelry: { name: '珠宝标签', data: jewelryLabelData },
    bread: { name: '面包标签', data: breadLabelData }
  };

  useEffect(() => {
    // 默认加载第一个模板
    loadTemplate('food');
  }, []);

  const loadTemplate = (templateKey) => {
    try {
      const templateData = templates[templateKey].data;
      const parsed = parseXPrinterData(templateData);
      setCurrentTemplate(parsed);
      setSelectedElement(null);
    } catch (error) {
      console.error('加载模板失败:', error);
      alert('加载模板失败: ' + error.message);
    }
  };

  const handleElementSelect = (element) => {
    setSelectedElement(element);
  };

  const handleElementAdd = (newElement) => {
    if (currentTemplate) {
      const updatedElements = [...currentTemplate.elements, newElement];
      setCurrentTemplate({
        ...currentTemplate,
        elements: updatedElements
      });
    }
  };

  const handleOneClickLayout = () => {
    if (currentTemplate && currentTemplate.elements.length > 0) {
      try {
        const layoutedElements = autoLayout(currentTemplate.elements, currentTemplate.canvas);
        setCurrentTemplate({
          ...currentTemplate,
          elements: layoutedElements
        });
        setSelectedElement(null);
      } catch (error) {
        console.error('一键排版失败:', error);
        alert('一键排版失败: ' + error.message);
      }
    } else {
      alert('请先添加一些元素到画布上');
    }
  };

  const handleClearCanvas = () => {
    if (currentTemplate) {
      // 只保留画布元素
      const canvasElements = currentTemplate.elements.filter(el =>
        el.elementType === "3" || el.elementType === 3
      );

      setCurrentTemplate({
        ...currentTemplate,
        elements: canvasElements
      });
      setSelectedElement(null);
    }
  };

  return (
    <AppContainer>
      <Sidebar>
        <h3>标签编辑器</h3>

        <div>
          <label>选择模板:</label>
          <TemplateSelector
            onChange={(e) => loadTemplate(e.target.value)}
            defaultValue="food"
          >
            {Object.entries(templates).map(([key, template]) => (
              <option key={key} value={key}>
                {template.name}
              </option>
            ))}
          </TemplateSelector>
        </div>

        <div>
          <label>缩放比例:</label>
          <TemplateSelector
            value={scale}
            onChange={(e) => setScale(parseFloat(e.target.value))}
          >
            <option value={0.5}>50%</option>
            <option value={0.75}>75%</option>
            <option value={1}>100%</option>
            <option value={1.25}>125%</option>
            <option value={1.5}>150%</option>
            <option value={2}>200%</option>
          </TemplateSelector>
        </div>

        {currentTemplate && (
          <div>
            <h4>画布信息</h4>
            <p>尺寸: {currentTemplate.canvas.width} × {currentTemplate.canvas.height} mm</p>
            <p>元素数量: {currentTemplate.elements.length}</p>

            {currentTemplate.canvas.templateBg && (
              <p>背景图: 已设置</p>
            )}
          </div>
        )}

        {selectedElement && (
          <ElementInfo>
            <h4>选中元素</h4>
            <p>类型: {selectedElement.typeName}</p>
            <p>位置: ({selectedElement.x.toFixed(1)}, {selectedElement.y.toFixed(1)})</p>
            <p>尺寸: {selectedElement.width.toFixed(1)} × {selectedElement.height.toFixed(1)}</p>
            {selectedElement.content && (
              <p>内容: {selectedElement.content}</p>
            )}
          </ElementInfo>
        )}
      </Sidebar>

      <MainArea>
        <ToolBar>
          <Button className="primary" onClick={handleOneClickLayout}>
            🪄 一键排版
          </Button>
          <Button onClick={handleClearCanvas}>
            🗑️ 清空画布
          </Button>
          <Button>撤销</Button>
          <Button>保存</Button>
        </ToolBar>

        {currentTemplate && (
          <DragPanel onDragStart={() => {}} />
        )}

        {currentTemplate ? (
          <Canvas
            width={currentTemplate.canvas.width}
            height={currentTemplate.canvas.height}
            backgroundImage={currentTemplate.canvas.templateBg}
            elements={currentTemplate.elements}
            scale={scale}
            onElementSelect={handleElementSelect}
            onElementAdd={handleElementAdd}
          />
        ) : (
          <div>加载中...</div>
        )}
      </MainArea>
    </AppContainer>
  );
}

export default App;
