
# data说明
如果是多排标签，且不复制首排，每一排标签都是一个数组，即 data 是一个二维数组，否则 data 是一个一维数组

# Key 字段说明

本文档旨在详细说明模板数据中使用的 JSON Key 字段，包括通用字段和各种标签类型特有的字段。

```ElementType定义
    public @interface ElementType {
        int ELEMENT_TYPE_MULTIPLE_SELECTION_LAYOUT = -1; // 多选布局;
        int ELEMENT_TYPE_CANVAS = 3; // 画布
        int ELEMENT_TYPE_TEXT = 1; // 文本
        int ELEMENT_TYPE_BAR_CODE = 2; // 一维码，条形码
        int ELEMENT_TYPE_LINE = 4; // 线条
        int ELEMENT_TYPE_LOGO = 5; // logo
        int ELEMENT_TYPE_PICTURE = 6; // 图片
        int ELEMENT_TYPE_QR_CODE = 7; // QR Code
        int ELEMENT_TYPE_TIME = 9; // 时间
        int ELEMENT_TYPE_CIRCULAR = 8; // 圆形
        int ELEMENT_TYPE_RECTANGLE = 11; // 矩形
        int ELEMENT_TYPE_TABLE = 10; // 表格
    }
```

## 布尔类型说明
如果是bool类型，转成字符串类型, 对应"true"和"false"


## 标签信息(ELEMENT_TYPE_CANVAS)
| 字段名 | 类型 | 描述 | 默认值 |
| `elementType` | `String` | `"3"` | 无 |
| `os` | `String` | 平台类型（`android`、`ios`, `web`） | 无 |
| `versionCode` | `String` | `0` | 版本信息，用于兼容，默认0 |
| `cableLabelDirection` | `int` | `小尾巴方向` | 无 |
| `cableLabelLength` | `float` | `小尾巴长度` | 无 |
| `execlFilePath` | `String` | excel文件路径 | 无 |
| `excelName` | `String` | excel文件名字 | 无 |
| `templateBg` | `String` | 背景图路径 | 无 |


## 通用字段(除ELEMENT_TYPE_CANVAS，控件共有)

| 字段名 | 类型 | 描述 | 默认值 |
|---|---|---|---|
| `elementType` | `int` | 标签类型，对应 `ElementType` 中的常量值。 | 无 |
| `rotational` | `String` | 标签的旋转角度。 | `"0"` |
| `x` | `String` | 标签左上角在画布上的 X 坐标（单位：mm）。 | 无 |
| `y` | `String` | 标签左上角在画布上的 Y 坐标（单位：mm）。 | 无 |
| `width` | `String` | 标签的宽度（单位：mm）。 | 无 |
| `height` | `String` | 标签的高度（单位：mm）。 | 无 |
| `localization` | `String` | 标签是否被锁定。 | `"false"` |
| `controlType` | `String` | 标签的控制类型，`2` 表示垂直拉伸，`3` 表示水平拉伸。 | 无 |
| `takePrint` | `String` | 标签是否参与打印。 | `"true"` |
| `mirrorImage` | `String` | 标签是否启用镜像。 | `"false"` |


## 文本标签 (ELEMENT_TYPE_TEXT)

| 字段名 | 类型 | 描述 | 默认值 |
|---|---|---|---|
| `inputDataType` | `String` | 输入数据类型，`4` 表示 Web 输入，其他值表示 `textLabelAttributeBean.getInputDataType()`。 | 无 |
| `transmutationValue` | `String` | 递增值。 | 无 |
| `excelKey` | `String` | Excel 键名。 | 无 |
| `excelPos` | `String` | 选择的 Excel 键位置。 | 无 |
| `content` | `String` | 文本内容。 | 无 |
| `wordSpace` | `String` | 字间距。 | `"0"` |
| `linesSpace` | `String` | 行间距。 | `"0"` |
| `fontType` | `String` | 字体类型。 | `"0"` |
| `textSize` | `String` | 文本大小。 | 无 |
| `hAlignment` | `String` | 水平对齐方式，`1` 为左对齐，`2` 为居中，`3` 为右对齐。 | 无 |
| `bold` | `String` | 是否加粗。 | `"false"` |
| `italic` | `String` | 是否斜体。 | `"false"` |
| `underline` | `String` | 是否下划线。 | `"false"` |
| `strikethrough` | `String` | 是否删除线。 | `"false"` |
| `showKeyName` | `String` | 是否显示键名。 | 无 |
| `prefix` | `String` | 前缀。 | 无 |
| `suffix` | `String` | 后缀。 | 无 |
| `blackWhiteReflection` | `String` | 是否黑白反转。 | `"false"` |
| `automaticHeightCalculation` | `String` | 自动高度计算。 | `"true"` |
| `lineWrap` | `String` | 自动换行。 | `"true"` |
| `flipX` | `String` | 是否水平翻转。 | `"false"` |
| `transmutationCount` | `String` | 递增计数。 | 无 |
| `transmutationType` | `Integer` | 递增类型。 | 无 |
| `transmutationNegativeNumbers` | `String` | 是否递增负数。 | `"false"` |
| `textArrangementType` | `String` | 文本排列类型。 | 无 |
| `arcAngle` | `String` | 弧形角度。 | 无 |
| `bindKey` | `String` | 绑定键。 | 无 |

## 二维码标签 (ELEMENT_TYPE_QR_CODE)

| 字段名 | 类型 | 描述 | 默认值 |
|---|---|---|---|
| `codeType` | `String` | 二维码类型。 | 无 |
| `whiteMargin` | `String` | 白边。 | `"0"` |
| `inputDataType` | `String` | 输入数据类型。 | 无 |
| `errorCorrectionLevel` | `String` | 错误纠正级别。 | 无 |
| `content` | `String` | 二维码内容。 | 无 |
| `Initial` | `String` | 初始值（当 `inputDataType` 为 `XlabelConstant.INPUT_DATA_TYPE_2` 时）。 | 无 |
| `prefix` | `String` | 前缀。 | 无 |
| `suffix` | `String` | 后缀。 | 无 |
| `transmutationValue` | `String` | 递增值。 | 无 |
| `increment` | `String` | 增量。 | 无 |
| `excelKey` | `String` | Excel 键名。 | 无 |
| `excelPos` | `String` | 选择的 Excel 键位置。 | 无 |
| `showKeyName` | `String` | 是否显示键名。 | 无 |
| `transmutationCount` | `String` | 递增计数。 | 无 |
| `transmutationType` | `Integer` | 递增类型。 | 无 |
| `transmutationNegativeNumbers` | `String` | 是否递增负数。 | `"false"` |
| `bindKey` | `String` | 绑定键。 | 无 |

## 时间标签 (ELEMENT_TYPE_TIME)

| 字段名 | 类型 | 描述 | 默认值 |
|---|---|---|---|
| `hAlignment` | `String` | 水平对齐方式，`1` 为左对齐，`2` 为居中，`3` 为右对齐。 | 无 |
| `italic` | `String` | 是否斜体。 | `"false"` |
| `bold` | `String` | 是否加粗。 | `"false"` |
| `blackWhiteReflection` | `String` | 是否黑白反转。 | `"false"` |
| `timeMigration` | `String` | 时间偏移。 | `"0"` |
| `hourMigration` | `String` | 小时偏移。 | `"0"` |
| `yearMigration` | `String` | 年偏移。 | `"0"` |
| `monthMigration` | `String` | 月偏移。 | `"0"` |
| `textSize` | `String` | 文本大小。 | 无 |
| `fontType` | `String` | 字体类型。 | 无 |
| `timeFormat` | `String` | 时间格式。 | 无 |
| `underline` | `String` | 是否下划线。 | `"false"` |
| `dateFormat` | `String` | 日期格式("yyyy-mm-hh")。 | 无 |
| `font_scale` | `String` | 字体缩放。 | 无 |
| `wordSpace` | `String` | 字间距。 | `"0"` |
| `linesSpace` | `String` | 行间距。 | `"0"` |
| `autoUpdate` | `String` | 是否自动更新。 | `"true"` |
| `lockTimeStamp` | `String` | 是否锁定时间戳。 | `"false"` |
| `strikethrough` | `String` | 是否删除线。 | `"false"` |
| `flipX` | `String` | 是否水平翻转。 | `"false"` |
| `textArrangementType` | `String` | 文本排列类型。 | 无 |
| `arcAngle` | `String` | 弧形角度。 | 无 |

## 条形码标签 (ELEMENT_TYPE_BAR_CODE)

| 字段名 | 类型 | 描述 | 默认值 |
|---|---|---|---|
| `showText` | `String` | 是否显示文本。 | 无 |
| `textAlignment` | `String` | 文本对齐方式。 | 无 |
| `barcodeType` | `String` | 条形码类型。 | 无 |
| `transmutationValue` | `String` | 递增值。 | 无 |
| `inputDataType` | `String` | 输入数据类型。 | 无 |
| `textSize` | `String` | 文本大小。 | 无 |
| `fontType` | `String` | 字体类型。 | 无 |
| `content` | `String` | 条形码内容。 | `"123456"` |
| `excelKey` | `String` | Excel 键名。 | 无 |
| `excelPos` | `String` | 选择的 Excel 键位置。 | 无 |
| `showKeyName` | `String` | 是否显示键名。 | 无 |
| `horizontalAlignment` | `String` | 水平对齐。 | `"true"` |
| `bold` | `String` | 是否加粗。 | `"false"` |
| `italic` | `String` | 是否斜体。 | `"false"` |
| `underline` | `String` | 是否下划线。 | `"false"` |
| `strikethrough` | `String` | 是否删除线。 | `"false"` |
| `transmutationCount` | `String` | 递增计数。 | 无 |
| `transmutationType` | `Integer` | 递增类型。 | 无 |
| `transmutationNegativeNumbers` | `String` | 是否递增负数。 | `"false"` |
| `bindKey` | `String` | 绑定键。 | 无 |

## 线条标签 (ELEMENT_TYPE_LINE)

| 字段名 | 类型 | 描述 | 默认值 |
|---|---|---|---|
| `lineType` | `String` | 线条类型。 | 无 |
| `dashType` | `String` | 虚线类型。 | 无 |
| `intervalValue` | `String` | 间隔值。 | 无 |
| `rotationAngle` | `String` | 旋转角度。 | 无 |

## Logo 标签 (ELEMENT_TYPE_LOGO)

| 字段名 | 类型 | 描述 | 默认值 |
|---|---|---|---|
| `content` | `String` | Logo 内容。 | 无 |
| `colorMode` | `String` | 颜色模式。 | 无 |
| `grayValue` | `String` | 灰度值。 | 无 |
| `tile` | `String` | 是否平铺。 | 无 |

## 图片标签 (ELEMENT_TYPE_PICTURE)

| 字段名 | 类型 | 描述 | 默认值 |
|---|---|---|---|
| `blackWhiteReflection` | `String` | 是否黑白反转。 | 无 |
| `grayValue` | `String` | 灰度值。 | 无 |
| `tile` | `String` | 是否平铺。 | 无 |
| `content` | `String` | 图片内容。 | 无 |
| `colorMode` | `String` | 颜色模式。 | 无 |

## 圆形标签 (ELEMENT_TYPE_CIRCULAR)

| 字段名 | 类型 | 描述 | 默认值 |
|---|---|---|---|
| `fill` | `String` | 是否填充。 | 无 |
| `borderWidth` | `String` | 边框宽度。 | 无 |
| `shapeType` | `String` | 形状类型。 | 无 |
| `circularWidth` | `String` | 圆形宽度（单位：mm）。 | 无 |
| `circularHeight` | `String` | 圆形高度（单位：mm）。 | 无 |

## 矩形标签 (ELEMENT_TYPE_RECTANGLE)

| 字段名 | 类型 | 描述 | 默认值 |
|---|---|---|---|
| `fill` | `String` | 是否填充。 | 无 |
| `borderWidth` | `String` | 边框宽度。 | 无 |
| `shapeType` | `String` | 形状类型。 | 无 |
| `rectangleWidth` | `String` | 矩形宽度（单位：mm）。 | 无 |
| `rectangleHeight` | `String` | 矩形高度（单位：mm）。 | 无 |

## 形状标签 (ELEMENT_TYPE_SHAPE)

| 字段名 | 类型 | 描述 | 默认值 |
|---|---|---|---|
| `type` | `int` | 标签类型，根据 `shapeType` 可能是圆形或矩形。 | 无 |
| `fill` | `String` | 是否填充。 | 无 |
| `borderWidth` | `String` | 边框宽度。 | 无 |
| `cornerWidth` | `String` | 圆角宽度（仅当 `shapeType` 为 `XlabelConstant.SHAPE_TYPE_CIRCULAR_RECTANGLE` 时）。 | 无 |
| `shapeType` | `String` | 形状类型。 | 无 |
| `rectangleWidth` | `String` | 矩形宽度（单位：mm，当 `shapeType` 为矩形时）。 | 无 |
| `rectangleHeight` | `String` | 矩形高度（单位：mm，当 `shapeType` 为矩形时）。 | 无 |
| `circularWidth` | `String` | 圆形宽度（单位：mm，当 `shapeType` 为圆形时）。 | 无 |
| `circularHeight` | `String` | 圆形高度（单位：mm，当 `shapeType` 为圆形时）。 | 无 |

## 表格标签 (ELEMENT_TYPE_TABLE)

| 字段名 | 类型 | 描述 | 默认值 |
|---|---|---|---|
| `rowHeights` | `JSONArray` | 行高数组（单位：mm）。 | 无 |
| `columnWidths` | `JSONArray` | 列宽数组（单位：mm）。 | 无 |
| `borderWidth` | `String` | 边框宽度。 | 无 |
| `cells` | `JSONArray` | 单元格数组（以左上角单元格为主）。 | 无 |

### 表格单元格 (cells) 字段

`cells` 数组中的每个对象代表一个单元格，包含以下字段：

| 字段名 | 类型 | 描述 | 默认值 |
|---|---|---|---|
| `col` | `String` | 列索引。 | 无 |
| `colSpan` | `String` | 列跨度。 | 无 |
| `hAlignment` | `String` | 水平对齐方式。 | 无 |
| `italic` | `String` | 是否斜体。 | 无 |
| `bold` | `String` | 是否加粗。 | 无 |
| `automaticHeightCalculation` | `Boolean` | 自动高度计算。 | `true` |
| `blackWhiteReflection` | `Boolean` | 是否黑白反转。 | 无 |
| `textSize` | `String` | 文本大小。 | 无 |
| `strikethrough` | `String` | 是否删除线。 | 无 |
| `fontType` | `String` | 字体类型。 | 无 |
| `horizontalAlignment` | `Boolean` | 水平对齐。 | `true` |
| `underline` | `String` | 是否下划线。 | 无 |
| `lineWrap` | `String` | 自动换行。 | `"true"` |
| `rowSpan` | `String` | 行跨度。 | 无 |
| `content` | `String` | 单元格内容。 | 无 |
| `row` | `String` | 行索引。 | 无 |
| `wordSpace` | `String` | 字间距。 | 无 |
| `linesSpace` | `String` | 行间距。 | 无 |
