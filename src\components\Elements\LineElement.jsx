import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

const LineContainer = styled.div`
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
`;

const Line = styled.div`
  position: absolute;
  background-color: black;
  transform-origin: top left;
  transform: ${props => {
    const transforms = [];
    if (props.rotationAngle) {
      transforms.push(`rotate(${props.rotationAngle}deg)`);
    }
    return transforms.length > 0 ? transforms.join(' ') : 'none';
  }};
`;

const SolidLine = styled(Line)`
  width: 100%;
  height: 1px;
`;

const DashedLine = styled(Line)`
  width: 100%;
  height: 1px;
  background: repeating-linear-gradient(
    to right,
    black 0,
    black ${props => props.dashLength}px,
    transparent ${props => props.dashLength}px,
    transparent ${props => props.dashLength + props.gapLength}px
  );
`;

const DottedLine = styled(Line)`
  width: 100%;
  height: 1px;
  background: repeating-linear-gradient(
    to right,
    black 0,
    black 1px,
    transparent 1px,
    transparent ${props => props.intervalValue}px
  );
`;

/**
 * 获取虚线样式参数
 * @param {string} dashType - 虚线类型
 * @param {number} intervalValue - 间隔值
 * @returns {Object} 虚线参数
 */
function getDashParams(dashType, intervalValue) {
  const interval = intervalValue || 2;
  
  switch (dashType) {
    case '1': // 短虚线
      return { dashLength: interval, gapLength: interval };
    case '2': // 长虚线
      return { dashLength: interval * 2, gapLength: interval };
    case '3': // 点线
      return { dashLength: 1, gapLength: interval };
    case '4': // 点划线
      return { dashLength: interval * 2, gapLength: interval };
    case '8': // 默认虚线
    default:
      return { dashLength: interval, gapLength: interval };
  }
}

/**
 * 线条元素组件
 * 
 * @param {Object} props - 组件属性
 * @param {Object} props.element - 线条元素数据
 * @param {number} props.scale - 缩放比例
 * @param {boolean} props.isSelected - 是否被选中
 */
const LineElement = ({ element, scale = 1, isSelected = false }) => {
  const {
    lineType = '1',
    dashType = '1',
    intervalValue = 2,
    rotationAngle = 0
  } = element;

  const renderLine = () => {
    switch (lineType) {
      case '1': // 实线
        return (
          <SolidLine 
            rotationAngle={rotationAngle}
          />
        );
      
      case '2': // 虚线
        const { dashLength, gapLength } = getDashParams(dashType, intervalValue * scale);
        return (
          <DashedLine 
            rotationAngle={rotationAngle}
            dashLength={dashLength}
            gapLength={gapLength}
          />
        );
      
      case '3': // 点线
        return (
          <DottedLine 
            rotationAngle={rotationAngle}
            intervalValue={intervalValue * scale}
          />
        );
      
      default:
        return (
          <SolidLine 
            rotationAngle={rotationAngle}
          />
        );
    }
  };

  return (
    <LineContainer>
      {renderLine()}
    </LineContainer>
  );
};

LineElement.propTypes = {
  element: PropTypes.object.isRequired,
  scale: PropTypes.number,
  isSelected: PropTypes.bool
};

export default LineElement;
