{"width": 70, "height": 50, "data": [{"elementType": "3", "hOffset": "0.0", "vOffset": "0.0", "printDensity": "4", "printSpeed": "0", "execlFilePath": "", "versionCode": 0, "os": "web", "templateBg": "", "cableLabelDirection": 2, "cableLabelLength": 0}, {"elementType": 11, "x": 0.9054531276848912, "y": 1.0450469621010439, "width": 67.42222245038423, "height": 9.68756317300938, "rotationAngle": 0, "lockLocation": "false", "controlType": "2", "takePrint": "true", "shapeType": "1", "fill": "false", "borderWidth": 0.2, "cornerWidth": 0, "scaleX": 1, "scaleY": 1, "lineType": "1"}, {"elementType": 11, "x": 0.9609278437964258, "y": 13.866701343113876, "width": 33.707531250520944, "height": 9.49361492607767, "rotationAngle": 0, "lockLocation": "false", "controlType": "2", "takePrint": "true", "shapeType": "1", "fill": "false", "borderWidth": 0.2, "cornerWidth": 0, "scaleX": 1, "scaleY": 1, "lineType": "1"}, {"elementType": 11, "x": 1.0035616572121306, "y": 26.45172347363361, "width": 67.83287668557574, "height": 10.50093462012565, "rotationAngle": 0, "lockLocation": "false", "controlType": "2", "takePrint": "true", "shapeType": "1", "fill": "false", "borderWidth": 0.2, "cornerWidth": 0, "scaleX": 1, "scaleY": 1, "lineType": "1"}, {"elementType": 1, "x": 0.9054531276848866, "y": 11.154168510155426, "width": 66.639414150992, "height": 2.8023999999999996, "rotationAngle": 0, "lockLocation": "false", "controlType": "3", "takePrint": "true", "inputDataType": "1", "transmutationValue": "1", "transmutationCount": "0", "transmutationType": 0, "excelKey": "", "excelPos": "-1", "content": "Part Number                                 Origin            Level", "wordSpace": 0, "linesSpace": 0, "lineWrap": "true", "automaticHeightCalculation": "true", "textSize": 7, "hAlignment": "1", "bold": "false", "italic": "false", "underline": "false", "strikethrough": "false", "showKeyName": "false", "blackWhiteReflection": "false", "flipX": "false", "font_scale": "1", "scaleX": 1, "scaleY": 1, "flipY": "false"}, {"elementType": 11, "x": 36.23340075285643, "y": 13.841802581686382, "width": 15.770718482419175, "height": 9.419046891502843, "rotationAngle": 0, "lockLocation": "false", "controlType": "2", "takePrint": "true", "shapeType": "1", "fill": "false", "borderWidth": 0.2, "cornerWidth": 0, "scaleX": 1, "scaleY": 1, "lineType": "1"}, {"elementType": 11, "x": 52.97292274509157, "y": 13.867322087861714, "width": 15.35475283297756, "height": 9.43122140969167, "rotationAngle": 0, "lockLocation": "false", "controlType": "2", "takePrint": "true", "shapeType": "1", "fill": "false", "borderWidth": 0.2, "cornerWidth": 0, "scaleX": 1, "scaleY": 1, "lineType": "1"}, {"elementType": 1, "x": 28.512124756335997, "y": 23.5988, "width": 12.975750487328, "height": 2.8023999999999996, "rotationAngle": 0, "lockLocation": "false", "controlType": "3", "takePrint": "true", "inputDataType": "1", "transmutationValue": "1", "transmutationCount": "0", "transmutationType": 0, "excelKey": "", "excelPos": "-1", "content": "Quantiqy", "wordSpace": 0, "linesSpace": 0, "lineWrap": "true", "automaticHeightCalculation": "true", "textSize": 7, "hAlignment": "1", "bold": "false", "italic": "false", "underline": "false", "strikethrough": "false", "showKeyName": "false", "blackWhiteReflection": "false", "flipX": "false", "font_scale": "1", "scaleX": 1, "scaleY": 1, "flipY": "false"}, {"elementType": 2, "x": 4.42, "y": 41.01999999999998, "width": 61.2, "height": 8, "rotationAngle": 0, "lockLocation": "false", "controlType": "2", "takePrint": "true", "inputDataType": "1", "transmutationValue": "1", "transmutationCount": "0", "transmutationType": 0, "excelKey": "", "content": "123456789012", "textSize": 9, "showText": "1", "barcodeType": "2", "textPosition": "bottom", "horizontalAlignment": "true", "bold": "false", "italic": "false", "underline": "false", "strikethrough": "false", "margin": "0", "scaleX": 1, "scaleY": 1, "excelPos": "-1", "showKeyName": "false"}, {"elementType": 1, "x": 10.226842105263149, "y": 37.42552631578947, "width": 49.5463157894737, "height": 3.2091999999999996, "rotationAngle": 0, "lockLocation": "false", "controlType": "3", "takePrint": "true", "inputDataType": "1", "transmutationValue": "1", "transmutationCount": "0", "transmutationType": 0, "excelKey": "", "excelPos": "-1", "content": "AGRICULTURAL MACNINERY PARTS", "wordSpace": 0, "linesSpace": 0, "lineWrap": "true", "automaticHeightCalculation": "true", "textSize": 8, "hAlignment": "1", "bold": "false", "italic": "false", "underline": "false", "strikethrough": "false", "showKeyName": "false", "blackWhiteReflection": "false", "flipX": "false", "font_scale": "1", "scaleX": 1, "scaleY": 1, "flipY": "false"}]}