import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

const QRContainer = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ccc;
  background-color: white;
  position: relative;
`;

const QRPlaceholder = styled.div`
  width: 80%;
  height: 80%;
  background: repeating-linear-gradient(
    45deg,
    #000,
    #000 2px,
    #fff 2px,
    #fff 4px
  );
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  color: white;
  text-shadow: 1px 1px 1px black;
`;

const QRText = styled.div`
  position: absolute;
  bottom: -20px;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 10px;
  color: black;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

/**
 * 二维码元素组件
 * 
 * @param {Object} props - 组件属性
 * @param {Object} props.element - 二维码元素数据
 * @param {number} props.scale - 缩放比例
 * @param {boolean} props.isSelected - 是否被选中
 */
const QRCodeElement = ({ element, scale = 1, isSelected = false }) => {
  const {
    content = '',
    codeType = 'QR_CODE',
    whiteMargin = 0,
    errorCorrectionLevel = 'M',
    prefix = '',
    suffix = ''
  } = element;

  // 处理前缀和后缀
  const displayContent = `${prefix}${content}${suffix}`;

  return (
    <QRContainer>
      <QRPlaceholder>
        QR
      </QRPlaceholder>
      {displayContent && (
        <QRText>
          {displayContent}
        </QRText>
      )}
    </QRContainer>
  );
};

QRCodeElement.propTypes = {
  element: PropTypes.object.isRequired,
  scale: PropTypes.number,
  isSelected: PropTypes.bool
};

export default QRCodeElement;
