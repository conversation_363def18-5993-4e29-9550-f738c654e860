import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import { ELEMENT_TYPES } from '../../utils/xprinterParser';

const DragPanelContainer = styled.div`
  padding: 5px 0;
  height: fit-content;
`;

const PanelTitle = styled.h4`
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #333;
  text-align: center;
`;

const ComponentGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;

  @media (max-width: 767px) {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  }

  @media (min-width: 768px) {
    grid-template-columns: 1fr;
    gap: 8px;
  }
`;

const DraggableComponent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px;
  border: 2px dashed #ccc;
  border-radius: 8px;
  cursor: grab;
  background-color: #f9f9f9;
  transition: all 0.2s ease;
  min-height: 60px;

  @media (min-width: 768px) {
    flex-direction: row;
    justify-content: flex-start;
    min-height: 50px;
  }

  &:hover {
    border-color: #007bff;
    background-color: #f0f8ff;
    transform: translateY(-2px);
  }

  &:active {
    cursor: grabbing;
    transform: scale(0.95);
  }
`;

const ComponentIcon = styled.div`
  font-size: 20px;
  margin-bottom: 4px;

  @media (min-width: 768px) {
    font-size: 24px;
    margin-bottom: 0;
    margin-right: 10px;
  }
`;

const ComponentLabel = styled.div`
  font-size: 12px;
  color: #666;
  text-align: center;

  @media (min-width: 768px) {
    font-size: 14px;
    font-weight: 500;
    text-align: left;
  }
`;

/**
 * 拖拽面板组件
 * 
 * @param {Object} props - 组件属性
 * @param {Function} props.onDragStart - 拖拽开始回调
 */
const DragPanel = ({ onDragStart }) => {
  
  // 可拖拽的组件列表
  const draggableComponents = [
    {
      type: ELEMENT_TYPES.TEXT,
      icon: '📝',
      label: '文本',
      defaultProps: {
        content: '文本内容',
        textSize: 14,
        fontType: '-2',
        hAlignment: 1,
        bold: false,
        italic: false,
        underline: false,
        strikethrough: false,
        wordSpace: 0,
        linesSpace: 0,
        lineWrap: true,
        blackWhiteReflection: false,
        width: 60,
        height: 20
      }
    },
    {
      type: ELEMENT_TYPES.QR_CODE,
      icon: '📱',
      label: '二维码',
      defaultProps: {
        content: 'QR内容',
        codeType: 'QR_CODE',
        whiteMargin: 0,
        errorCorrectionLevel: 'M',
        width: 30,
        height: 30
      }
    },
    {
      type: ELEMENT_TYPES.BAR_CODE,
      icon: '📊',
      label: '条形码',
      defaultProps: {
        content: '123456789',
        barcodeType: '1',
        showText: true,
        textAlignment: '1',
        textSize: 12,
        width: 60,
        height: 25
      }
    },
    {
      type: ELEMENT_TYPES.LINE,
      icon: '➖',
      label: '线条',
      defaultProps: {
        lineType: '1',
        dashType: '1',
        intervalValue: 2,
        rotationAngle: 0,
        width: 50,
        height: 2
      }
    }
  ];

  const handleDragStart = (e, component) => {
    // 设置拖拽数据
    const dragData = {
      type: 'component',
      componentType: component.type,
      defaultProps: component.defaultProps
    };
    
    e.dataTransfer.setData('text/plain', JSON.stringify(dragData));
    e.dataTransfer.effectAllowed = 'copy';
    
    if (onDragStart) {
      onDragStart(dragData);
    }
  };

  return (
    <DragPanelContainer>
      <ComponentGrid>
        {draggableComponents.map((component, index) => (
          <DraggableComponent
            key={index}
            draggable
            onDragStart={(e) => handleDragStart(e, component)}
          >
            <ComponentIcon>{component.icon}</ComponentIcon>
            <ComponentLabel>{component.label}</ComponentLabel>
          </DraggableComponent>
        ))}
      </ComponentGrid>
    </DragPanelContainer>
  );
};

DragPanel.propTypes = {
  onDragStart: PropTypes.func
};

export default DragPanel;
