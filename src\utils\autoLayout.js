/**
 * 自动排版工具
 * 实现一键排版功能，自动调整元素位置和间距
 */

import { ELEMENT_TYPES } from './xprinterParser';

/**
 * 一键排版主函数
 * @param {Array} elements - 元素数组
 * @param {Object} canvasInfo - 画布信息
 * @returns {Array} 排版后的元素数组
 */
export function autoLayout(elements, canvasInfo) {
  if (!elements || elements.length === 0) {
    return elements;
  }

  // 过滤掉画布元素
  const layoutElements = elements.filter(el => 
    el.elementType !== ELEMENT_TYPES.CANVAS && 
    el.elementType !== "3" && 
    el.elementType !== 3
  );

  if (layoutElements.length === 0) {
    return elements;
  }

  // 按元素类型分组
  const groupedElements = groupElementsByType(layoutElements);
  
  // 应用排版策略
  const layoutResult = applyLayoutStrategy(groupedElements, canvasInfo);
  
  // 合并回原数组
  return mergeLayoutResult(elements, layoutResult);
}

/**
 * 按元素类型分组
 * @param {Array} elements - 元素数组
 * @returns {Object} 分组后的元素
 */
function groupElementsByType(elements) {
  const groups = {
    text: [],
    barcode: [],
    qrcode: [],
    line: [],
    other: []
  };

  elements.forEach(element => {
    const type = parseInt(element.elementType);
    switch (type) {
      case ELEMENT_TYPES.TEXT:
        groups.text.push(element);
        break;
      case ELEMENT_TYPES.BAR_CODE:
        groups.barcode.push(element);
        break;
      case ELEMENT_TYPES.QR_CODE:
        groups.qrcode.push(element);
        break;
      case ELEMENT_TYPES.LINE:
        groups.line.push(element);
        break;
      default:
        groups.other.push(element);
    }
  });

  return groups;
}

/**
 * 应用排版策略
 * @param {Object} groupedElements - 分组的元素
 * @param {Object} canvasInfo - 画布信息
 * @returns {Array} 排版后的元素数组
 */
function applyLayoutStrategy(groupedElements, canvasInfo) {
  const { width: canvasWidth, height: canvasHeight } = canvasInfo;
  const margin = 5; // 边距 (mm)
  const spacing = 3; // 元素间距 (mm)

  let currentY = margin;
  const layoutedElements = [];

  // 排版顺序：文本 -> 条形码/二维码 -> 线条 -> 其他
  const layoutOrder = ['text', 'qrcode', 'barcode', 'line', 'other'];

  // 检查是否有标题样式的文本
  const hasTitle = groupedElements.text.some(el =>
    parseFloat(el.textSize) > 12 || el.bold === "true"
  );

  // 检查是否有多个文本元素
  const hasMultipleTexts = groupedElements.text.length > 1;

  // 选择排版策略
  let layoutStrategy = 'standard';

  if (hasTitle && hasMultipleTexts) {
    layoutStrategy = 'titleContent';
  } else if (groupedElements.text.length > 3) {
    layoutStrategy = 'list';
  }

  // 应用选定的排版策略
  switch (layoutStrategy) {
    case 'titleContent':
      return titleContentLayout(groupedElements, canvasInfo);
    case 'list':
      return listLayout(groupedElements, canvasInfo);
    default:
      return standardLayout(groupedElements, canvasInfo);
  }
}

/**
 * 标准排版 - 左对齐，垂直排列
 * @param {Object} groupedElements - 分组的元素
 * @param {Object} canvasInfo - 画布信息
 * @returns {Array} 排版后的元素数组
 */
function standardLayout(groupedElements, canvasInfo) {
  const { width: canvasWidth, height: canvasHeight } = canvasInfo;
  const margin = 5; // 边距 (mm)
  const spacing = 3; // 元素间距 (mm)

  let currentY = margin;
  const layoutedElements = [];

  // 排版顺序：文本 -> 条形码/二维码 -> 线条 -> 其他
  const layoutOrder = ['text', 'qrcode', 'barcode', 'line', 'other'];

  layoutOrder.forEach(groupType => {
    const elements = groupedElements[groupType];
    if (elements.length === 0) return;

    // 按原始Y坐标排序，保持相对顺序
    elements.sort((a, b) => parseFloat(a.y) - parseFloat(b.y));

    elements.forEach((element, index) => {
      const layoutedElement = { ...element };

      // 左对齐
      layoutedElement.x = margin;

      // 垂直排列，保持间距
      layoutedElement.y = currentY;

      // 确保元素不超出画布边界
      const elementWidth = parseFloat(element.width);
      const elementHeight = parseFloat(element.height);

      // 调整宽度以适应画布
      if (elementWidth > canvasWidth - 2 * margin) {
        layoutedElement.width = canvasWidth - 2 * margin;
      }

      // 更新下一个元素的Y位置
      currentY += elementHeight + spacing;

      layoutedElements.push(layoutedElement);
    });
  });

  return layoutedElements;
}

/**
 * 合并排版结果回原数组
 * @param {Array} originalElements - 原始元素数组
 * @param {Array} layoutedElements - 排版后的元素数组
 * @returns {Array} 合并后的元素数组
 */
function mergeLayoutResult(originalElements, layoutedElements) {
  const result = [...originalElements];
  
  // 创建一个映射，用于快速查找排版后的元素
  const layoutMap = new Map();
  layoutedElements.forEach((element, index) => {
    layoutMap.set(element, index);
  });

  // 替换非画布元素
  let layoutIndex = 0;
  for (let i = 0; i < result.length; i++) {
    const element = result[i];
    const type = parseInt(element.elementType);
    
    if (type !== ELEMENT_TYPES.CANVAS && type !== 3) {
      if (layoutIndex < layoutedElements.length) {
        result[i] = layoutedElements[layoutIndex];
        layoutIndex++;
      }
    }
  }

  return result;
}

/**
 * 智能排版 - 基于元素内容和类型的高级排版
 * @param {Array} elements - 元素数组
 * @param {Object} canvasInfo - 画布信息
 * @returns {Array} 排版后的元素数组
 */
export function smartLayout(elements, canvasInfo) {
  // 分析元素特征
  const analysis = analyzeElements(elements);
  
  // 根据分析结果选择排版策略
  if (analysis.hasTitle && analysis.hasContent) {
    return titleContentLayout(elements, canvasInfo, analysis);
  } else if (analysis.isListLike) {
    return listLayout(elements, canvasInfo);
  } else {
    return autoLayout(elements, canvasInfo);
  }
}

/**
 * 分析元素特征
 * @param {Array} elements - 元素数组
 * @returns {Object} 分析结果
 */
function analyzeElements(elements) {
  const textElements = elements.filter(el => 
    parseInt(el.elementType) === ELEMENT_TYPES.TEXT
  );

  let hasTitle = false;
  let hasContent = false;
  let isListLike = false;

  if (textElements.length > 0) {
    // 检查是否有标题样式的文本（较大字体或加粗）
    hasTitle = textElements.some(el => 
      parseFloat(el.textSize) > 12 || el.bold === "true"
    );

    // 检查是否有内容文本
    hasContent = textElements.length > 1;

    // 检查是否像列表（多个相似的文本元素）
    isListLike = textElements.length > 2 && 
      textElements.every(el => el.textSize === textElements[0].textSize);
  }

  return {
    hasTitle,
    hasContent,
    isListLike,
    textCount: textElements.length,
    totalElements: elements.length
  };
}

/**
 * 标题-内容布局
 * @param {Object} groupedElements - 分组的元素
 * @param {Object} canvasInfo - 画布信息
 * @returns {Array} 排版后的元素数组
 */
function titleContentLayout(groupedElements, canvasInfo) {
  const { width: canvasWidth, height: canvasHeight } = canvasInfo;
  const margin = 5; // 边距 (mm)
  const spacing = 3; // 元素间距 (mm)

  let currentY = margin;
  const layoutedElements = [];

  // 找出标题元素（最大字体或加粗的文本）
  const textElements = [...groupedElements.text];
  textElements.sort((a, b) => {
    const aSize = parseFloat(a.textSize) || 0;
    const bSize = parseFloat(b.textSize) || 0;
    const aIsBold = a.bold === "true";
    const bIsBold = b.bold === "true";

    if (aIsBold && !bIsBold) return -1;
    if (!aIsBold && bIsBold) return 1;
    return bSize - aSize;
  });

  // 第一个元素作为标题，居中显示
  if (textElements.length > 0) {
    const titleElement = { ...textElements[0] };
    titleElement.x = (canvasWidth - parseFloat(titleElement.width)) / 2;
    titleElement.y = currentY;
    titleElement.hAlignment = 2; // 居中对齐

    layoutedElements.push(titleElement);
    currentY += parseFloat(titleElement.height) + spacing * 2; // 标题后多留点空间

    // 移除已处理的标题
    textElements.shift();
  }

  // 处理剩余文本元素
  textElements.forEach(element => {
    const layoutedElement = { ...element };
    layoutedElement.x = margin;
    layoutedElement.y = currentY;

    layoutedElements.push(layoutedElement);
    currentY += parseFloat(layoutedElement.height) + spacing;
  });

  // 处理其他类型的元素
  const otherGroups = ['qrcode', 'barcode', 'line', 'other'];
  otherGroups.forEach(groupType => {
    const elements = groupedElements[groupType];
    if (elements.length === 0) return;

    elements.forEach(element => {
      const layoutedElement = { ...element };
      layoutedElement.x = margin;
      layoutedElement.y = currentY;

      layoutedElements.push(layoutedElement);
      currentY += parseFloat(layoutedElement.height) + spacing;
    });
  });

  return layoutedElements;
}

/**
 * 列表布局
 * @param {Object} groupedElements - 分组的元素
 * @param {Object} canvasInfo - 画布信息
 * @returns {Array} 排版后的元素数组
 */
function listLayout(groupedElements, canvasInfo) {
  const { width: canvasWidth, height: canvasHeight } = canvasInfo;
  const margin = 5; // 边距 (mm)
  const spacing = 2; // 列表项间距更小 (mm)

  let currentY = margin;
  const layoutedElements = [];

  // 处理文本元素为列表项
  const textElements = [...groupedElements.text];
  textElements.sort((a, b) => parseFloat(a.y) - parseFloat(b.y));

  // 检查是否有标题
  let hasTitle = false;
  if (textElements.length > 0) {
    const firstElement = textElements[0];
    hasTitle = parseFloat(firstElement.textSize) > 12 || firstElement.bold === "true";

    if (hasTitle) {
      // 标题居中
      const titleElement = { ...firstElement };
      titleElement.x = (canvasWidth - parseFloat(titleElement.width)) / 2;
      titleElement.y = currentY;
      titleElement.hAlignment = 2; // 居中对齐

      layoutedElements.push(titleElement);
      currentY += parseFloat(titleElement.height) + spacing * 2;

      // 移除已处理的标题
      textElements.shift();
    }
  }

  // 处理列表项
  textElements.forEach((element, index) => {
    const layoutedElement = { ...element };

    // 左对齐，添加缩进
    layoutedElement.x = margin + 3; // 缩进3mm
    layoutedElement.y = currentY;

    // 添加列表项标记（如果不是第一行）
    if (index > 0 || !hasTitle) {
      // 创建列表项标记（圆点）
      const bulletElement = {
        elementType: 8, // 圆形
        x: margin,
        y: currentY + parseFloat(layoutedElement.height) / 2 - 1, // 垂直居中
        width: 2,
        height: 2,
        fill: "true"
      };

      layoutedElements.push(bulletElement);
    }

    layoutedElements.push(layoutedElement);
    currentY += parseFloat(layoutedElement.height) + spacing;
  });

  // 处理其他类型的元素
  const otherGroups = ['qrcode', 'barcode', 'line', 'other'];
  otherGroups.forEach(groupType => {
    const elements = groupedElements[groupType];
    if (elements.length === 0) return;

    elements.forEach(element => {
      const layoutedElement = { ...element };
      layoutedElement.x = margin;
      layoutedElement.y = currentY;

      layoutedElements.push(layoutedElement);
      currentY += parseFloat(layoutedElement.height) + spacing;
    });
  });

  return layoutedElements;
}
