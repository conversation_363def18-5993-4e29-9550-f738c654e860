import { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import ElementRenderer from '../Elements';

const CanvasContainer = styled.div`
  position: relative;
  background-color: white;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin: 0 auto;
`;

const CanvasElement = styled.div`
  position: absolute;
  transform-origin: top left;
  cursor: ${props => props.isSelected ? 'move' : 'pointer'};
  user-select: none;

  &:hover {
    outline: ${props => props.isSelected ? '2px solid #007bff' : '1px dashed #ccc'};
  }
`;

/**
 * 画布组件 - 用于渲染和管理标签元素
 *
 * @param {Object} props - 组件属性
 * @param {number} props.width - 画布宽度(mm)
 * @param {number} props.height - 画布高度(mm)
 * @param {string} props.backgroundImage - 背景图片URL
 * @param {Array} props.elements - 元素数组
 * @param {number} props.scale - 缩放比例
 * @param {Function} props.onElementSelect - 元素选择回调
 * @param {Function} props.onElementAdd - 添加元素回调
 */
const Canvas = ({
  width,
  height,
  backgroundImage,
  elements = [],
  scale = 1,
  onElementSelect,
  onElementAdd
}) => {
  const canvasRef = useRef(null);
  const [selectedElement, setSelectedElement] = useState(null);
  const [dragInfo, setDragInfo] = useState(null);

  // 固定画布显示宽度为400px，根据实际尺寸计算比例
  const CANVAS_DISPLAY_WIDTH = 400;
  const MM_TO_PX = CANVAS_DISPLAY_WIDTH / width; // 毫米到像素的转换比例
  
  const handleElementClick = (element, event) => {
    event.stopPropagation();
    setSelectedElement(element);
    if (onElementSelect) {
      onElementSelect(element);
    }
  };

  // 元素拖拽开始
  const handleElementMouseDown = (element, event) => {
    event.stopPropagation();
    event.preventDefault();

    setSelectedElement(element);
    if (onElementSelect) {
      onElementSelect(element);
    }

    const canvasRect = canvasRef.current.getBoundingClientRect();
    const startX = event.clientX - canvasRect.left;
    const startY = event.clientY - canvasRect.top;

    setDragInfo({
      element,
      startX,
      startY,
      originalX: parseFloat(element.x),
      originalY: parseFloat(element.y)
    });
  };

  // 鼠标移动处理
  const handleMouseMove = (event) => {
    if (!dragInfo) return;

    event.preventDefault();
    const canvasRect = canvasRef.current.getBoundingClientRect();
    const currentX = event.clientX - canvasRect.left;
    const currentY = event.clientY - canvasRect.top;

    const deltaX = (currentX - dragInfo.startX) / MM_TO_PX;
    const deltaY = (currentY - dragInfo.startY) / MM_TO_PX;

    const newX = Math.max(0, Math.min(width - parseFloat(dragInfo.element.width), dragInfo.originalX + deltaX));
    const newY = Math.max(0, Math.min(height - parseFloat(dragInfo.element.height), dragInfo.originalY + deltaY));

    // 更新元素位置
    const updatedElements = elements.map(el =>
      el === dragInfo.element
        ? { ...el, x: newX, y: newY }
        : el
    );

    if (onElementAdd) {
      // 使用 onElementAdd 来更新元素位置
      onElementAdd(null, updatedElements);
    }
  };

  // 鼠标释放处理
  const handleMouseUp = () => {
    setDragInfo(null);
  };

  const handleCanvasClick = () => {
    setSelectedElement(null);
    if (onElementSelect) {
      onElementSelect(null);
    }
  };

  // 拖拽处理
  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
  };

  const handleDrop = (e) => {
    e.preventDefault();

    try {
      const dragData = JSON.parse(e.dataTransfer.getData('text/plain'));

      if (dragData.type === 'component') {
        // 获取画布相对位置
        const canvasRect = canvasRef.current.getBoundingClientRect();
        const x = (e.clientX - canvasRect.left) / (MM_TO_PX * scale);
        const y = (e.clientY - canvasRect.top) / (MM_TO_PX * scale);

        // 创建新元素
        const newElement = {
          elementType: dragData.componentType,
          x: Math.max(0, x - dragData.defaultProps.width / 2),
          y: Math.max(0, y - dragData.defaultProps.height / 2),
          rotational: 0,
          localization: false,
          controlType: "3",
          takePrint: true,
          mirrorImage: false,
          ...dragData.defaultProps
        };

        if (onElementAdd) {
          onElementAdd(newElement);
        }
      }
    } catch (error) {
      console.error('处理拖拽数据失败:', error);
    }
  };

  // 渲染元素
  const renderElements = () => {
    return elements.map((element, index) => {
      // 跳过画布元素
      if (element.elementType === "3" || element.elementType === 3) {
        return null;
      }

      const isSelected = selectedElement === element;
      
      // 计算元素位置和尺寸
      const style = {
        left: `${parseFloat(element.x) * MM_TO_PX}px`,
        top: `${parseFloat(element.y) * MM_TO_PX}px`,
        width: `${parseFloat(element.width) * MM_TO_PX}px`,
        height: `${parseFloat(element.height) * MM_TO_PX}px`,
        border: isSelected ? '2px solid #007bff' : 'none',
        // 根据元素类型添加其他样式
      };

      return (
        <CanvasElement
          key={index}
          style={style}
          isSelected={isSelected}
          onClick={(e) => handleElementClick(element, e)}
          onMouseDown={(e) => handleElementMouseDown(element, e)}
        >
          <ElementRenderer
            element={element}
            scale={scale}
            isSelected={isSelected}
            mmToPx={MM_TO_PX}
          />
        </CanvasElement>
      );
    });
  };

  // 计算显示尺寸
  const displayWidth = CANVAS_DISPLAY_WIDTH;
  const displayHeight = height * MM_TO_PX;

  // 添加鼠标事件监听
  useEffect(() => {
    if (dragInfo) {
      // 添加全局鼠标事件监听
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);

      return () => {
        // 清理事件监听
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [dragInfo]);

  return (
    <CanvasContainer
      ref={canvasRef}
      style={{
        width: `${displayWidth}px`,
        height: `${displayHeight}px`,
        backgroundImage: backgroundImage ? `url(${backgroundImage})` : 'none',
        backgroundSize: 'cover',
      }}
      onClick={handleCanvasClick}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      {renderElements()}
    </CanvasContainer>
  );
};

Canvas.propTypes = {
  width: PropTypes.number.isRequired,
  height: PropTypes.number.isRequired,
  backgroundImage: PropTypes.string,
  elements: PropTypes.array,
  scale: PropTypes.number,
  onElementSelect: PropTypes.func,
  onElementAdd: PropTypes.func
};

export default Canvas;
