import React from 'react';
import PropTypes from 'prop-types';
import { ELEMENT_TYPES } from '../../utils/xprinterParser';
import TextElement from './TextElement';
import LineElement from './LineElement';
import QRCodeElement from './QRCodeElement';
import BarCodeElement from './BarCodeElement';

/**
 * 元素渲染器 - 根据元素类型渲染对应的组件
 *
 * @param {Object} props - 组件属性
 * @param {Object} props.element - 元素数据
 * @param {number} props.scale - 缩放比例
 * @param {boolean} props.isSelected - 是否被选中
 * @param {number} props.mmToPx - 毫米到像素的转换比例
 */
const ElementRenderer = ({ element, scale = 1, isSelected = false, mmToPx = 1 }) => {
  const elementType = parseInt(element.elementType);

  switch (elementType) {
    case ELEMENT_TYPES.TEXT:
      return (
        <TextElement
          element={element}
          scale={scale}
          isSelected={isSelected}
          mmToPx={mmToPx}
        />
      );

    case ELEMENT_TYPES.LINE:
      return (
        <LineElement 
          element={element} 
          scale={scale} 
          isSelected={isSelected} 
        />
      );

    case ELEMENT_TYPES.QR_CODE:
      return (
        <QRCodeElement
          element={element}
          scale={scale}
          isSelected={isSelected}
        />
      );

    case ELEMENT_TYPES.BAR_CODE:
      return (
        <BarCodeElement
          element={element}
          scale={scale}
          isSelected={isSelected}
        />
      );

    case ELEMENT_TYPES.TIME:
      // TODO: 实现时间组件
      return (
        <div style={{ 
          width: '100%', 
          height: '100%', 
          border: '1px dashed #ccc',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '12px',
          color: '#666'
        }}>
          时间元素
        </div>
      );

    case ELEMENT_TYPES.PICTURE:
    case ELEMENT_TYPES.LOGO:
      // TODO: 实现图片组件
      return (
        <div style={{ 
          width: '100%', 
          height: '100%', 
          border: '1px dashed #ccc',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '12px',
          color: '#666'
        }}>
          图片元素
        </div>
      );

    case ELEMENT_TYPES.CIRCULAR:
      // TODO: 实现圆形组件
      return (
        <div style={{ 
          width: '100%', 
          height: '100%', 
          border: '1px solid black',
          borderRadius: '50%'
        }} />
      );

    case ELEMENT_TYPES.RECTANGLE:
      // TODO: 实现矩形组件
      return (
        <div style={{ 
          width: '100%', 
          height: '100%', 
          border: '1px solid black'
        }} />
      );

    case ELEMENT_TYPES.TABLE:
      // TODO: 实现表格组件
      return (
        <div style={{ 
          width: '100%', 
          height: '100%', 
          border: '1px dashed #ccc',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '12px',
          color: '#666'
        }}>
          表格元素
        </div>
      );

    default:
      return (
        <div style={{ 
          width: '100%', 
          height: '100%', 
          border: '1px dashed red',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '12px',
          color: 'red'
        }}>
          未知元素类型: {elementType}
        </div>
      );
  }
};

ElementRenderer.propTypes = {
  element: PropTypes.object.isRequired,
  scale: PropTypes.number,
  isSelected: PropTypes.bool,
  mmToPx: PropTypes.number
};

export default ElementRenderer;
