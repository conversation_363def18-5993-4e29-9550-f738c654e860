import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

const TextContainer = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: ${props => {
    switch (props.vAlignment) {
      case 'top': return 'flex-start';
      case 'bottom': return 'flex-end';
      default: return 'center';
    }
  }};
  justify-content: ${props => {
    switch (props.hAlignment) {
      case 1: return 'flex-start';  // 左对齐
      case 3: return 'flex-end';    // 右对齐
      default: return 'center';     // 居中
    }
  }};
  overflow: hidden;
  word-wrap: ${props => props.lineWrap ? 'break-word' : 'normal'};
  white-space: ${props => props.lineWrap ? 'normal' : 'nowrap'};
`;

const TextContent = styled.div`
  font-size: ${props => props.textSize}px;
  font-weight: ${props => props.bold ? 'bold' : 'normal'};
  font-style: ${props => props.italic ? 'italic' : 'normal'};
  text-decoration: ${props => {
    const decorations = [];
    if (props.underline) decorations.push('underline');
    if (props.strikethrough) decorations.push('line-through');
    return decorations.length > 0 ? decorations.join(' ') : 'none';
  }};
  letter-spacing: ${props => props.wordSpace}px;
  line-height: ${props => props.textSize + props.linesSpace}px;
  color: ${props => props.blackWhiteReflection ? 'white' : 'black'};
  background-color: ${props => props.blackWhiteReflection ? 'black' : 'transparent'};
  transform: ${props => {
    const transforms = [];
    if (props.flipX) transforms.push('scaleX(-1)');
    if (props.rotational) transforms.push(`rotate(${props.rotational}deg)`);
    return transforms.length > 0 ? transforms.join(' ') : 'none';
  }};
  font-family: ${props => getFontFamily(props.fontType)};
  white-space: pre-line;
`;

/**
 * 根据字体类型获取字体族
 * @param {string} fontType - 字体类型
 * @returns {string} CSS 字体族
 */
function getFontFamily(fontType) {
  switch (fontType) {
    case '0':
      return 'Arial, sans-serif';
    case '-1':
      return 'Times New Roman, serif';
    case '-2':
      return 'Microsoft YaHei, SimHei, sans-serif';
    default:
      return 'Arial, sans-serif';
  }
}

/**
 * 文本元素组件
 * 
 * @param {Object} props - 组件属性
 * @param {Object} props.element - 文本元素数据
 * @param {number} props.scale - 缩放比例
 * @param {boolean} props.isSelected - 是否被选中
 */
const TextElement = ({ element, scale = 1, isSelected = false }) => {
  const {
    content = '',
    textSize = 12,
    fontType = '0',
    hAlignment = 1,
    bold = false,
    italic = false,
    underline = false,
    strikethrough = false,
    wordSpace = 0,
    linesSpace = 0,
    lineWrap = true,
    blackWhiteReflection = false,
    flipX = false,
    rotational = 0,
    prefix = '',
    suffix = ''
  } = element;

  // 处理前缀和后缀
  const displayContent = `${prefix}${content}${suffix}`;

  return (
    <TextContainer
      hAlignment={hAlignment}
      lineWrap={lineWrap}
    >
      <TextContent
        textSize={textSize * scale}
        fontType={fontType}
        bold={bold}
        italic={italic}
        underline={underline}
        strikethrough={strikethrough}
        wordSpace={wordSpace * scale}
        linesSpace={linesSpace * scale}
        blackWhiteReflection={blackWhiteReflection}
        flipX={flipX}
        rotational={rotational}
      >
        {displayContent}
      </TextContent>
    </TextContainer>
  );
};

TextElement.propTypes = {
  element: PropTypes.object.isRequired,
  scale: PropTypes.number,
  isSelected: PropTypes.bool
};

export default TextElement;
