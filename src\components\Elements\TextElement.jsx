import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

const TextContainer = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: flex-start;
  justify-content: ${props => {
    switch (props.hAlignment) {
      case 1: return 'flex-start';  // 左对齐
      case 3: return 'flex-end';    // 右对齐
      default: return 'center';     // 居中
    }
  }};
  word-wrap: ${props => props.lineWrap ? 'break-word' : 'normal'};
  white-space: ${props => props.lineWrap ? 'normal' : 'nowrap'};
  overflow: visible;
`;

const TextContent = styled.span`
  font-size: ${props => props.textSize}pt; /* textSize单位是磅(pt) */
  font-weight: ${props => props.bold ? 'bold' : 'normal'};
  font-style: ${props => props.italic ? 'italic' : 'normal'};
  text-decoration: ${props => {
    const decorations = [];
    if (props.underline) decorations.push('underline');
    if (props.strikethrough) decorations.push('line-through');
    return decorations.length > 0 ? decorations.join(' ') : 'none';
  }};
  letter-spacing: ${props => props.wordSpace * props.mmToPx}px; /* wordSpace是mm，需要转换为px */
  line-height: ${props => props.textSize * 1.2}pt; /* 行高基于字体大小，linesSpace另外处理 */
  color: ${props => props.blackWhiteReflection ? 'white' : 'black'};
  background-color: ${props => props.blackWhiteReflection ? 'black' : 'transparent'};
  transform: ${props => {
    const transforms = [];
    // 注释掉 flipX 功能，避免文本反向显示难以阅读
    // if (props.flipX) transforms.push('scaleX(-1)');
    if (props.rotational) transforms.push(`rotate(${props.rotational}deg)`);
    return transforms.length > 0 ? transforms.join(' ') : 'none';
  }};
  font-family: ${props => getFontFamily(props.fontType)};
  white-space: pre-line;

  /* 选中状态的边框 - 紧贴文字内容，只添加少量padding */
  border: ${props => props.isSelected ? '1px solid #007bff' : '1px solid transparent'};
  padding: ${props => props.isSelected ? '2px 4px' : '2px 4px'};
  margin: ${props => props.linesSpace * props.mmToPx}px 0;
  display: inline-block;
  box-sizing: border-box;
`;

/**
 * 根据字体类型获取字体族
 * @param {string} fontType - 字体类型
 * @returns {string} CSS 字体族
 */
function getFontFamily(fontType) {
  switch (fontType) {
    case '0':
      return 'Arial, sans-serif';
    case '-1':
      return 'Times New Roman, serif';
    case '-2':
      return 'Microsoft YaHei, SimHei, sans-serif';
    default:
      return 'Arial, sans-serif';
  }
}

/**
 * 文本元素组件
 *
 * @param {Object} props - 组件属性
 * @param {Object} props.element - 文本元素数据
 * @param {number} props.scale - 缩放比例
 * @param {boolean} props.isSelected - 是否被选中
 * @param {number} props.mmToPx - 毫米到像素的转换比例
 */
const TextElement = ({ element, scale = 1, isSelected = false, mmToPx = 1 }) => {
  const {
    content = '',
    textSize = 12,
    fontType = '0',
    hAlignment = 1,
    bold = false,
    italic = false,
    underline = false,
    strikethrough = false,
    wordSpace = 0,
    linesSpace = 0,
    lineWrap = true,
    blackWhiteReflection = false,
    flipX = false,
    rotational = 0,
    prefix = '',
    suffix = ''
  } = element;

  // 处理前缀和后缀
  const displayContent = `${prefix}${content}${suffix}`;

  return (
    <TextContainer
      hAlignment={hAlignment}
      lineWrap={lineWrap}
    >
      <TextContent
        textSize={textSize} // textSize单位是磅(pt)，直接使用
        fontType={fontType}
        bold={bold}
        italic={italic}
        underline={underline}
        strikethrough={strikethrough}
        wordSpace={wordSpace}
        linesSpace={linesSpace}
        blackWhiteReflection={blackWhiteReflection}
        flipX={flipX}
        rotational={rotational}
        mmToPx={mmToPx}
        isSelected={isSelected}
      >
        {displayContent}
      </TextContent>
    </TextContainer>
  );
};

TextElement.propTypes = {
  element: PropTypes.object.isRequired,
  scale: PropTypes.number,
  isSelected: PropTypes.bool,
  mmToPx: PropTypes.number
};

export default TextElement;
