import React from 'react';
import styled from 'styled-components';
import PropTypes from 'prop-types';

const BarCodeContainer = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: white;
  position: relative;
`;

const BarCodeGraphic = styled.div`
  width: 90%;
  height: ${props => props.showText ? '70%' : '90%'};
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const Bar = styled.div`
  height: 100%;
  background-color: black;
  width: ${props => props.width}px;
`;

const BarCodeText = styled.div`
  margin-top: 5px;
  font-size: ${props => props.textSize}px;
  font-family: monospace;
  text-align: center;
`;

/**
 * 条形码元素组件
 * 
 * @param {Object} props - 组件属性
 * @param {Object} props.element - 条形码元素数据
 * @param {number} props.scale - 缩放比例
 * @param {boolean} props.isSelected - 是否被选中
 */
const BarCodeElement = ({ element, scale = 1, isSelected = false }) => {
  const {
    content = '123456',
    barcodeType = '1',
    showText = true,
    textAlignment = '1',
    textSize = 12,
    bold = false,
    italic = false,
    underline = false,
    strikethrough = false
  } = element;

  // 生成随机条形码图形
  const generateBars = () => {
    const bars = [];
    const contentLength = content.length;
    const totalBars = contentLength * 4; // 每个字符生成4个条
    
    for (let i = 0; i < totalBars; i++) {
      const width = Math.random() * 2 + 1; // 1-3px宽度
      bars.push(
        <Bar 
          key={i} 
          width={width * scale}
        />
      );
    }
    
    return bars;
  };

  return (
    <BarCodeContainer>
      <BarCodeGraphic showText={showText}>
        {generateBars()}
      </BarCodeGraphic>
      
      {showText && (
        <BarCodeText 
          textSize={textSize * scale}
          style={{
            fontWeight: bold ? 'bold' : 'normal',
            fontStyle: italic ? 'italic' : 'normal',
            textDecoration: `${underline ? 'underline' : ''} ${strikethrough ? 'line-through' : ''}`.trim()
          }}
        >
          {content}
        </BarCodeText>
      )}
    </BarCodeContainer>
  );
};

BarCodeElement.propTypes = {
  element: PropTypes.object.isRequired,
  scale: PropTypes.number,
  isSelected: PropTypes.bool
};

export default BarCodeElement;
